[{"question": "what are the top customers by sales in europe", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nWHERE  r.r_name = 'EUROPE'\nGROUP BY customer_name\nORDER BY total_sales desc"}, {"question": "total sales for each region?", "answer": "SELECT r.r_name as region,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nGROUP BY region\nORDER BY total_sales desc;"}, {"question": "What is the average annual revenue for 'Brand#23' parts that are shipped in 'MED BOX', where the quantity shipped is less than 20% of the average quantity shipped for that part?", "answer": "SELECT \n\tAVG(l_extendedprice * (1 - l_discount)) as avg_annual_revenue\nFROM \n\tSNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n\tJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey\nWHERE \n\tp_brand = 'Brand#23' AND p_container = 'MED BOX' AND l_quantity < (0.2 * (\n\t\tSELECT \n\t\t\tAVG(l_quantity) \n\t\tFROM \n\t\t\tSNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n\t\t\tJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey\n\t\tWHERE \n\t\t\tp_brand = 'Brand#23' AND p_container = 'MED BOX'\n\t))\nGROUP BY \n\textract(year from l_shipdate)"}, {"question": "show me the pie chart of the market share in customer table", "answer": "SELECT count(*) as total_customers\r\nFROM   snowflake_sample_data.tpch_sf1.customer;"}, {"question": "Name the 10 countries having the most customer. Show the name of the countries and the number of customers.", "answer": "SELECT n.n_name as country_name,\n       count(c.c_custkey) as number_of_customers\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nGROUP BY country_name\nORDER BY number_of_customers desc limit 10;"}, {"question": "What are the names of the countries in the European region?", "answer": "SELECT n.n_name AS country\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nWHERE r.r_name = 'EUROPE'"}, {"question": "What is the number of customers in each region?", "answer": "SELECT r.r_name AS region, COUNT(DISTINCT c.c_custkey) AS number_of_customers\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\nGROUP BY region"}, {"question": "Who are the top 100 customers who placed orders with a total quantity of items exceeding 300, and what are the details of these orders?", "answer": "SELECT \r\n  c.c_custkey, \r\n  c.c_name, \r\n  SUM(l.l_quantity) AS total_quantity, \r\n  o.o_orderkey, \r\n  o.o_orderdate, \r\n  o.o_totalprice, \r\n  o.o_comment\r\nFROM \r\n  snowflake_sample_data.tpch_sf1.customer c\r\n  JOIN snowflake_sample_data.tpch_sf1.orders o ON c.c_custkey = o.o_custkey\r\n  JOIN snowflake_sample_data.tpch_sf1.lineitem l ON o.o_orderkey = l.l_orderkey\r\nGROUP BY \r\n  c.c_custkey, \r\n  c.c_name, \r\n  o.o_orderkey, \r\n  o.o_orderdate, \r\n  o.o_totalprice, \r\n  o.o_comment\r\nHAVING \r\n  SUM(l.l_quantity) > 300\r\nORDER BY \r\n  total_quantity DESC\r\nLIMIT \r\n  100"}, {"question": "Who is the top customer in each region?", "answer": "with ranked_sales as (SELECT r.r_name as region_name,\n                             c.c_name as customer_name,\n                             sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\n                             rank() OVER (PARTITION BY r.r_name\n                                          ORDER BY sum(l.l_extendedprice * (1 - l.l_discount)) desc) as sales_rank\n                      FROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n                              ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n                              ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n                              ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n                              ON n.n_regionkey = r.r_regionkey\n                      GROUP BY region_name, customer_name)\nSELECT region_name,\n       customer_name,\n       total_sales\nFROM   ranked_sales\nWHERE  sales_rank = 1\nORDER BY region_name;"}, {"question": "Which countries have the most failed orders ?", "answer": "SELECT n.n_name as country_name,\n       count(o.o_orderkey) as failed_orders\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey\nWHERE  o.o_orderstatus = 'F'\nGROUP BY country_name\nORDER BY failed_orders desc;"}, {"question": "How many customers have a given number of orders, excluding those orders with special requests? I want the results sorted by the number of customers in descending order and, for equal counts of customers, by the number of orders in descending order.", "answer": "SELECT num_orders, COUNT(*) AS num_customers\nFROM (\n    SELECT o.o_custkey, COUNT(DISTINCT o.o_orderkey) AS num_orders\n    FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o\n    LEFT JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\n    WHERE l.l_commitdate = l.l_receiptdate\n    GROUP BY o.o_custkey\n)\nGROUP BY num_orders\nORDER BY num_customers DESC, num_orders DESC"}, {"question": "what are the 10 biggest suppliers, along with the orders.", "answer": "SELECT s.s_name as supplier_name,\n       count(distinct l.l_orderkey) as order_count\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.supplier s\n        ON l.l_suppkey = s.s_suppkey\nGROUP BY supplier_name\nORDER BY order_count desc limit 10"}, {"question": "What is the top 3 customers in China? What are their names?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'CHINA'\nGROUP BY customer_name\nORDER BY total_sales desc limit 3;"}, {"question": "which 10 customers have had the largest increase in total order value compared to last quarter", "answer": "SELECT c.c_name as customer_name,\n       sum(case when o.o_orderdate >= dateadd(month, -3, date_trunc('quarter', current_date())) and\n                     o.o_orderdate < date_trunc('quarter',\n                                                current_date()) then l.l_extendedprice * (1 - l.l_discount)\n                else 0 end) as last_quarter_total,\n       sum(case when o.o_orderdate >= dateadd(month, -6, date_trunc('quarter', current_date())) and\n                     o.o_orderdate < dateadd(month,\n                                             -3,\n                                             date_trunc('quarter', current_date())) then l.l_extendedprice * (1 - l.l_discount)\n                else 0 end) as previous_quarter_total,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as current_quarter_total\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey\nGROUP BY customer_name having last_quarter_total > 0 and previous_quarter_total > 0\nORDER BY (current_quarter_total - previous_quarter_total) desc limit 10;"}, {"question": "What are the top 6 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 6"}, {"question": "What are the top 20 customers that have the highest gross value of returned parts?", "answer": "SELECT \n    c.c_name, \n    SUM(l.l_extendedprice * l.l_discount) as returned_parts_gross_value\nFROM \n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nWHERE \n    l.l_returnflag = 'R'\nGROUP BY \n    c.c_name\nORDER BY \n    returned_parts_gross_value DESC\nLIMIT \n    20"}, {"question": "Can you provide the count of orders for each priority level that were placed in the third quarter of 1993 (from July 1, 1993, to September 30, 1993) and have at least one line item which was received later than its commit date?", "answer": "select\r\n\to_orderpriority,\r\n\tcount(*) as order_count\r\nfrom\r\n\torders\r\nwhere\r\n\to_orderdate >= date '1993-07-01'\r\n\tAND o_orderdate < DATEADD(month, 3, '1993-07-01')\r\n\tand exists (\r\n\t\tselect\r\n\t\t\t*\r\n\t\tfrom\r\n\t\t\tlineitem\r\n\t\twhere\r\n\t\t\tl_orderkey = o_orderkey\r\n\t\t\tand l_commitdate < l_receiptdate\r\n\t)\r\ngroup by\r\n\to_orderpriority\r\norder by\r\n\to_orderpriority;\r\n"}, {"question": "What are the names of the top six customers with the highest total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 6"}, {"question": "Calculate the max  order amount for the top 10 nations?", "answer": "SELECT n.n_name as country_name,\n       max(l.l_extendedprice * (1 - l.l_discount)) as max_order_amount\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey\nGROUP BY country_name\nORDER BY max_order_amount desc limit 10;"}, {"question": "What's the top customer by sales in each region?", "answer": "with ranked_sales as (SELECT r.r_name as region_name,\n                             c.c_name as customer_name,\n                             sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\n                             rank() OVER (PARTITION BY r.r_name\n                                          ORDER BY sum(l.l_extendedprice * (1 - l.l_discount)) desc) as sales_rank\n                      FROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n                              ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n                              ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n                              ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n                              ON n.n_regionkey = r.r_regionkey\n                      GROUP BY 1, 2)\nSELECT region_name,\n       customer_name,\n       total_sales\nFROM   ranked_sales\nWHERE  sales_rank = 1;"}, {"question": "what top 10 countries have the most customers? show the name of the country and number of customers", "answer": "This question seems to be a duplicate. Here's the SQL code again:\n\n```\nSELECT n.n_name AS country_name, COUNT(c.c_custkey) AS number_of_customers\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\nGROUP BY country_name\nORDER BY number_of_customers DESC\nLIMIT 10\n```"}, {"question": "Can you give me the names of the top 100 suppliers in Saudi Arabia, along with the count of their finalized orders, where the orders were received after their committed dates and none of the other suppliers for those orders were late?", "answer": "SELECT\n  s.s_suppkey AS supplier_key,\n  s.s_name AS supplier_name,\n  COUNT(DISTINCT o.o_orderkey) AS order_count\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON s.s_suppkey = l.l_suppkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l2 ON o.o_orderkey = l2.l_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s2 ON l2.l_suppkey = s2.s_suppkey\nWHERE\n  s.s_nationkey = (\n    SELECT n.n_nationkey\n    FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n    WHERE n.n_name = 'SAUDI ARABIA'\n  )\n  AND l.l_receiptdate > l.l_commitdate\n  AND NOT EXISTS (\n    SELECT *\n    FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l3\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o2 ON l3.l_orderkey = o2.o_orderkey\n    WHERE l3.l_receiptdate > l3.l_commitdate\n    AND o2.o_orderkey = o.o_orderkey\n    AND l3.l_suppkey <> s.s_suppkey\n  )\nGROUP BY supplier_key, supplier_name\nORDER BY order_count DESC\nLIMIT 100;"}, {"question": "What is the total revenue generated from orders that meet any of the following three sets of conditions:  Orders of parts from 'Brand#12' that are packaged in 'SM CASE', 'SM BOX', 'SM PACK', or 'SM PKG', with a quantity between 1 and 11, a size between 1 and 5, and a shipping mode of 'AIR' or 'AIR REG', with instructions to 'DELIVER IN PERSON'.  Orders of parts from 'Brand#23' that are packaged in 'MED BAG', 'MED BOX', 'MED PKG', or 'MED PACK', with a quantity between 10 and 20, a size between 1 and 10, and a shipping mode of 'AIR' or 'AIR REG', with instructions to 'DELIVER IN PERSON'.  Orders of parts from 'Brand#34' that are packaged in 'LG CASE', 'LG BOX', 'LG PACK', or 'LG PKG', with a quantity between 20 and 30, a size between 1 and 15, and a shipping mode of 'AIR' or 'AIR REG', with instructions to 'DELIVER IN PERSON'.", "answer": "select\r\n\tsum(l_extendedprice* (1 - l_discount)) as revenue\r\nfrom\r\n\tlineitem,\r\n\tpart\r\nwhere\r\n\t(\r\n\t\tp_partkey = l_partkey\r\n\t\tand p_brand = 'Brand#12'\r\n\t\tand p_container in ('SM CASE', 'SM BOX', 'SM PACK', 'SM PKG')\r\n\t\tand l_quantity >= 1 and l_quantity <= 1 + 10\r\n\t\tand p_size between 1 and 5\r\n\t\tand l_shipmode in ('AIR', 'AIR REG')\r\n\t\tand l_shipinstruct = 'DELIVER IN PERSON'\r\n\t)\r\n\tor\r\n\t(\r\n\t\tp_partkey = l_partkey\r\n\t\tand p_brand = 'Brand#23'\r\n\t\tand p_container in ('MED BAG', 'MED BOX', 'MED PKG', 'MED PACK')\r\n\t\tand l_quantity >= 10 and l_quantity <= 10 + 10\r\n\t\tand p_size between 1 and 10\r\n\t\tand l_shipmode in ('AIR', 'AIR REG')\r\n\t\tand l_shipinstruct = 'DELIVER IN PERSON'\r\n\t)\r\n\tor\r\n\t(\r\n\t\tp_partkey = l_partkey\r\n\t\tand p_brand = 'Brand#34'\r\n\t\tand p_container in ('LG CASE', 'LG BOX', 'LG PACK', 'LG PKG')\r\n\t\tand l_quantity >= 20 and l_quantity <= 20 + 10\r\n\t\tand p_size between 1 and 15\r\n\t\tand l_shipmode in ('AIR', 'AIR REG')\r\n\t\tand l_shipinstruct = 'DELIVER IN PERSON'\r\n\t);\r\n\r\n"}, {"question": "Who are the top 5 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 5;"}, {"question": "What are the top 10 customers in the United States by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 10"}, {"question": "Can you provide a list of nations in the 'ASIA' region and the total revenue generated from their customers for orders placed in the year 1994? I need this data ordered by the revenue in descending order.", "answer": "select\r\n\tn_name,\r\n\tsum(l_extendedprice * (1 - l_discount)) as revenue\r\nfrom\r\n\tcustomer,\r\n\torders,\r\n\tlineitem,\r\n\tsupplier,\r\n\tnation,\r\n\tregion\r\nwhere\r\n\tc_custkey = o_custkey\r\n\tand l_orderkey = o_orderkey\r\n\tand l_suppkey = s_suppkey\r\n\tand c_nationkey = s_nationkey\r\n\tand s_nationkey = n_nationkey\r\n\tand n_regionkey = r_regionkey\r\n\tand r_name = 'ASIA'\r\n\tand o_orderdate >= date '1994-01-01'\r\n\tAND o_orderdate < DATEADD(year, 1, '1994-01-01')\r\ngroup by\r\n\tn_name\r\norder by\r\n\trevenue desc;\r\n"}, {"question": "what's the top 5 regions by sales?", "answer": "SELECT r.r_name as region_name,\r\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\r\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\r\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\r\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\r\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\r\n        ON n.n_regionkey = r.r_regionkey\r\nGROUP BY region_name\r\nORDER BY total_sales desc limit 5;"}, {"question": "Could you provide a summary of our shipped line items prior to September 3, 1998, grouped by return status and line status? I need to know the total quantity, base price, discounted price, price charged after tax, the average quantity, price, discount rate, and the number of orders for each group.", "answer": "select\r\n\tl_returnflag,\r\n\tl_linestatus,\r\n\tsum(l_quantity) as sum_qty,\r\n\tsum(l_extendedprice) as sum_base_price,\r\n\tsum(l_extendedprice * (1 - l_discount)) as sum_disc_price,\r\n\tsum(l_extendedprice * (1 - l_discount) * (1 + l_tax)) as sum_charge,\r\n\tavg(l_quantity) as avg_qty,\r\n\tavg(l_extendedprice) as avg_price,\r\n\tavg(l_discount) as avg_disc,\r\n\tcount(*) as count_order\r\nfrom\r\n\tlineitem\r\nwhere\r\n\tl_shipdate <= DATEADD(day, 90, '1998-12-01')\r\ngroup by\r\n\tl_returnflag,\r\n\tl_linestatus\r\norder by\r\n\tl_returnflag,\r\n\tl_linestatus;"}, {"question": "Revenue from top 10 customers", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as revenue\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY revenue desc limit 10"}, {"question": "which customers based in iran the most total revenue? show customer name and number of orders", "answer": "SELECT \n    c.c_name AS customer_name, \n    COUNT(DISTINCT o.o_orderkey) AS number_of_orders, \n    SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_revenue\nFROM \n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey \nWHERE \n    n.n_name = 'IRAN'\nGROUP BY \n    customer_name \nORDER BY \n    total_revenue DESC \nLIMIT 10;"}, {"question": "美国前10大客户是谁?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 10"}, {"question": " for each region get me the top 5 most valued orders for each region's along with the customer's name", "answer": "with ranked_sales as (SELECT r.r_name as region_name,\n                             c.c_name as customer_name,\n                             o.o_orderkey as order_key,\n                             sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\n                             rank() OVER (PARTITION BY r.r_name\n                                          ORDER BY sum(l.l_extendedprice * (1 - l.l_discount)) desc) as sales_rank\n                      FROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n                              ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n                              ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n                              ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n                              ON n.n_regionkey = r.r_regionkey\n                      GROUP BY region_name, customer_name, order_key)\nSELECT region_name,\n       customer_name,\n       order_key,\n       total_sales\nFROM   ranked_sales\nWHERE  sales_rank <= 5;"}, {"question": "what top 10 countries have the most customers? show the name of the country and number of customers.", "answer": "SELECT n.n_name AS country_name, COUNT(c.c_custkey) AS number_of_customers\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\nGROUP BY country_name\nORDER BY number_of_customers DESC\nLIMIT 10"}, {"question": "What is the count of customers and the total account balance from countries with specific country codes ('13', '31', '23', '29', '30', '18', '17'), where the customer's account balance is greater than the average account balance of customers from these specific countries, and the customers have not placed any orders?", "answer": "SELECT\n  COUNT(DISTINCT c.c_custkey) AS customer_count,\n  SUM(c.c_acctbal) AS total_account_balance\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\nWHERE\n  n.n_name IN ('ARGENTINA', 'CANADA', 'GERMANY', 'UNITED KINGDOM', 'FRANCE', 'CANADA', 'UNITED STATES')\n  AND c.c_acctbal > (SELECT AVG(c2.c_acctbal) FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c2 JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n2 ON c2.c_nationkey = n2.n_nationkey WHERE n2.n_name IN ('ARGENTIN<PERSON>', '<PERSON><PERSON><PERSON>', 'GERMANY', 'UNITED KINGDOM', 'FRANCE', 'CANADA', 'UNITED STATES'))\n  AND c.c_custkey NOT IN (SELECT o.o_custkey FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o)"}, {"question": "what is the name of the 10 biggest suppliers, along with the orders.", "answer": "SELECT s.s_name as supplier_name,\n       count(distinct l.l_orderkey) as order_count\nFROM   snowflake_sample_data.tpch_sf1.supplier s join snowflake_sample_data.tpch_sf1.lineitem l\n        ON s.s_suppkey = l.l_suppkey\nGROUP BY supplier_name\nORDER BY order_count desc limit 10;"}, {"question": "Who is the largest customer by sales in each region?", "answer": "The corrected SQL query is:\n```\nwith ranked_sales as (SELECT r.r_name as region_name,\n                             c.c_name as customer_name,\n                             sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\n                             rank() OVER (PARTITION BY r.r_name\n                                          ORDER BY sum(l.l_extendedprice * (1 - l.l_discount)) desc) as sales_rank\n                      FROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n                              ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n                              ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n                              ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n                              ON n.n_regionkey = r.r_regionkey\n                      GROUP BY 1, 2)\nSELECT region_name,\n       customer_name,\n       total_sales\nFROM   ranked_sales\nWHERE  sales_rank = 1\nORDER BY region_name asc;\n``` \n\nI replaced the `r.r_regionkey` in the `PARTITION BY` clause with `r.r_name` because `r.r_regionkey` is not a valid group by expression. I also used the `GROUP BY 1, 2` syntax instead of explicitly specifying the column names in the `GROUP BY` clause."}, {"question": "What is the count of line items purchased with each discount rate in each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  l.l_discount AS discount,\n  COUNT(*) AS count\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY\n  region_name,\n  discount\nORDER BY\n  region_name,\n  discount"}, {"question": "What are the top 11 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 11"}, {"question": "Which supplier has the lowest average part cost in each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  s.s_name AS supplier_name,\n  MIN(ps.ps_supplycost) AS lowest_avg_cost\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON n.n_nationkey = s.s_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PARTSUPP ps ON s.s_suppkey = ps.ps_suppkey\n  JOIN (\n    SELECT\n      n.n_regionkey,\n      MIN(ps.ps_supplycost) AS min_supplycost\n    FROM\n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON n.n_nationkey = s.s_nationkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PARTSUPP ps ON s.s_suppkey = ps.ps_suppkey\n    GROUP BY\n      n.n_regionkey\n  ) min_costs ON n.n_regionkey = min_costs.n_regionkey AND ps.ps_supplycost = min_costs.min_supplycost\nGROUP BY region_name, supplier_name;"}, {"question": "What are the top 8 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 8"}, {"question": "What are the top 10 customers's phone in the US?", "answer": "SELECT c.c_name as customer_name,\n       c.c_phone as customer_phone\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nORDER BY c.c_acctbal desc limit 10;"}, {"question": "What product had the largest price range in the last 24 months?", "answer": "SELECT p.p_name,\n       stddev(l.l_extendedprice * (1 - l.l_discount)) as price_std_dev\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.part p\n        ON l.l_partkey = p.p_partkey join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey\nWHERE  o.o_orderdate between dateadd(month, -24, current_date())\n   and current_date()\nGROUP BY p.p_name\nORDER BY price_std_dev desc limit 1;"}, {"question": "Retrieve customer names with highest orders from Africa region", "answer": "SELECT c.c_name as customer_name,\r\n       count(o.o_orderkey) as order_count\r\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\r\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.nation n\r\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\r\n        ON n.n_regionkey = r.r_regionkey\r\nWHERE  r.r_name = 'AFRICA'\r\nGROUP BY customer_name\r\nORDER BY order_count desc limit 10"}, {"question": "What are the top 10 customers in the US?", "answer": "SELECT c.c_name as customer_name,\r\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\r\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\r\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\r\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\r\n        ON c.c_nationkey = n.n_nationkey\r\nWHERE  n.n_name = 'UNITED STATES'\r\nGROUP BY customer_name\r\nORDER BY total_sales desc limit 10"}, {"question": "What is the total revenue for the year 1994 from line items with a quantity less than 24 and a discount within 0.01 of 0.06?", "answer": "select\r\n\tsum(l_extendedprice * l_discount) as revenue\r\nfrom\r\n\tsnowflake_sample_data.tpch_sf1.lineitem\r\nwhere\r\n\tl_shipdate >= date '1994-01-01'\r\n\tAND l_shipdate < DATEADD(year, 1, '1994-01-01')\r\n\tand l_discount between .06 - 0.01 and .06 + 0.01\r\n\tand l_quantity < 24;\r\n"}, {"question": "can you please show me limit of 5 customers", "answer": "SELECT n.n_name as country_name,\n       count(c.c_custkey) as number_of_customers\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nGROUP BY country_name\nORDER BY number_of_customers desc limit 5;"}, {"question": "What are the names of the top 5 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_quantity * l.l_extendedprice) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 5"}, {"question": "Calculate the highest total order amount for the top 10 nations?", "answer": "SELECT n.n_name as country_name,\r\n       round(sum(l.l_extendedprice * (1 - l.l_discount)),2) as total_order_amount\r\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\r\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\r\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\r\n        ON o.o_orderkey = l.l_orderkey\r\nGROUP BY country_name\r\nORDER BY total_order_amount desc limit 10"}, {"question": "What is the total sales and number of customers for each region?", "answer": "SELECT r.r_name AS region, COUNT(DISTINCT c.c_custkey) AS number_of_customers, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY region"}, {"question": "For the years 1995 and 1996, what is the annual revenue from shipping transactions between France and Germany, regardless of which nation is the supplier and which is the customer? I need this information grouped by supplier nation, customer nation, and year", "answer": "select\r\n\tsupp_nation,\r\n\tcust_nation,\r\n\tl_year,\r\n\tsum(volume) as revenue\r\nfrom\r\n\t(\r\n\t\tselect\r\n\t\t\tn1.n_name as supp_nation,\r\n\t\t\tn2.n_name as cust_nation,\r\n\t\t\textract(year from l_shipdate) as l_year,\r\n\t\t\tl_extendedprice * (1 - l_discount) as volume\r\n\t\tfrom\r\n\t\t\tsupplier,\r\n\t\t\tlineitem,\r\n\t\t\torders,\r\n\t\t\tcustomer,\r\n\t\t\tnation n1,\r\n\t\t\tnation n2\r\n\t\twhere\r\n\t\t\ts_suppkey = l_suppkey\r\n\t\t\tand o_orderkey = l_orderkey\r\n\t\t\tand c_custkey = o_custkey\r\n\t\t\tand s_nationkey = n1.n_nationkey\r\n\t\t\tand c_nationkey = n2.n_nationkey\r\n\t\t\tand (\r\n\t\t\t\t(n1.n_name = 'FRANCE' and n2.n_name = 'GERMANY')\r\n\t\t\t\tor (n1.n_name = 'GERMANY' and n2.n_name = 'FRANCE')\r\n\t\t\t)\r\n\t\t\tand l_shipdate between date '1995-01-01' and date '1996-12-31'\r\n\t) as shipping\r\ngroup by\r\n\tsupp_nation,\r\n\tcust_nation,\r\n\tl_year\r\norder by\r\n\tsupp_nation,\r\n\tcust_nation,\r\n\tl_year;\r\n\r\n"}, {"question": "how many customers are in each region", "answer": "SELECT r.r_name as region_name,\n       count(distinct c.c_custkey) as customer_count\nFROM   snowflake_sample_data.tpch_sf1.region r join snowflake_sample_data.tpch_sf1.nation n\n        ON r.r_regionkey = n.n_regionkey join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nGROUP BY region_name\nORDER BY region_name asc;"}, {"question": "Calculate the total order amount for the top 10 nation?", "answer": "SELECT n.n_name as nation_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_order_amount\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey\nGROUP BY nation_name\nORDER BY total_order_amount desc limit 10;"}, {"question": "Which supplier has the lowest average part cost for brass parts in each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  s.s_name AS supplier_name,\n  MIN(ps.ps_supplycost) AS lowest_avg_cost\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON n.n_nationkey = s.s_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PARTSUPP ps ON s.s_suppkey = ps.ps_suppkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON ps.ps_partkey = p.p_partkey\nWHERE p.p_type like '%BRASS'\nGROUP BY region_name, supplier_name;"}, {"question": "show me the pie chart of the market share by market segment in customer table", "answer": "SELECT c.c_mktsegment as market_segment,\r\n       count(*) as market_segment_count\r\nFROM   snowflake_sample_data.tpch_sf1.customer c\r\nGROUP BY market_segment;"}, {"question": "Can you provide a list of the top 10 orders, based on revenue, for customers in the 'BUILDING' market segment that were placed before March 15, 1995, but shipped after this date? I need the order key, total revenue, order date, and shipping priority for each.", "answer": "select\r\n\tl_orderkey,\r\n\tsum(l_extendedprice * (1 - l_discount)) as revenue,\r\n\to_orderdate,\r\n\to_shippriority\r\nfrom\r\n\tcustomer,\r\n\torders,\r\n\tlineitem\r\nwhere\r\n\tc_mktsegment = 'BUILDING'\r\n\tand c_custkey = o_custkey\r\n\tand l_orderkey = o_orderkey\r\n\tand o_orderdate < date '1995-03-15'\r\n\tand l_shipdate > date '1995-03-15'\r\ngroup by\r\n\tl_orderkey,\r\n\to_orderdate,\r\n\to_shippriority\r\norder by\r\n\trevenue desc,\r\n\to_orderdate limit 10;"}, {"question": "What are the top 7 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 7;"}, {"question": "Could you provide a list of the top 100 suppliers in Europe based on their account balance, who supply brass parts of size 15 at the lowest cost? I need to know the supplier's name, their address, phone number, and any additional comments, the name of their nation, as well as the part key and manufacturer.", "answer": "select\r\n\ts_acctbal,\r\n\ts_name,\r\n\tn_name,\r\n\tp_partkey,\r\n\tp_mfgr,\r\n\ts_address,\r\n\ts_phone,\r\n\ts_comment\r\nfrom\r\n\tpart,\r\n\tsupplier,\r\n\tpartsupp,\r\n\tnation,\r\n\tregion\r\nwhere\r\n\tp_partkey = ps_partkey\r\n\tand s_suppkey = ps_suppkey\r\n\tand p_size = 15\r\n\tand p_type like '%BRASS'\r\n\tand s_nationkey = n_nationkey\r\n\tand n_regionkey = r_regionkey\r\n\tand r_name = 'EUROPE'\r\n\tand ps_supplycost = (\r\n\t\tselect\r\n\t\t\tmin(ps_supplycost)\r\n\t\tfrom\r\n\t\t\tpartsupp,\r\n\t\t\tsupplier,\r\n\t\t\tnation,\r\n\t\t\tregion\r\n\t\twhere\r\n\t\t\tp_partkey = ps_partkey\r\n\t\t\tand s_suppkey = ps_suppkey\r\n\t\t\tand s_nationkey = n_nationkey\r\n\t\t\tand n_regionkey = r_regionkey\r\n\t\t\tand r_name = 'EUROPE'\r\n\t)\r\norder by\r\n\ts_acctbal desc,\r\n\tn_name,\r\n\ts_name,\r\n\tp_partkey LIMIT 100;"}, {"question": "What is the percentage of revenue that came from promotional parts (those having type starting with 'PROMO') shipped in September 1995?", "answer": "SELECT \n  (SUM(l.l_extendedprice * (1 - l.l_discount)) / \n  (SELECT \n    SUM(l.l_extendedprice * (1 - l.l_discount)) \n  FROM \n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey \n  WHERE \n    p.p_type LIKE 'PROMO%') * 100) AS promo_sales_pct\nFROM \n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l \n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey \n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey \nWHERE \n  p.p_type LIKE 'PROMO%'\n  AND o.o_orderdate >= DATEADD(month, -2, '1995-10-01')\n  AND o.o_orderdate < DATEADD(month, 1, '1995-10-01');"}, {"question": "Who are the top 5 customers in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 5;"}, {"question": "What are the number of countries in each region, in descending order?", "answer": "SELECT r.r_name AS region_name, COUNT(DISTINCT n.n_name) AS num_countries\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r \nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\nGROUP BY region_name\nORDER BY num_countries DESC"}, {"question": "What are the top 3 customers by sales ranking for each of the top 2 regions by total sales?", "answer": "WITH\n  sales_by_region AS (\n    SELECT\n      r.r_name AS region_name,\n      c.c_name AS customer_name,\n      SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\n    FROM\n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n      JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\n    GROUP BY\n      region_name,\n      customer_name\n  ),\n  ranked_sales AS (\n    SELECT\n      region_name,\n      customer_name,\n      total_sales,\n      RANK() OVER (\n        PARTITION BY region_name\n        ORDER BY total_sales DESC\n      ) AS sales_rank\n    FROM\n      sales_by_region\n  )\nSELECT *\nFROM ranked_sales\nWHERE region_name IN (\n  SELECT\n    r.r_name\n  FROM\n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\n  GROUP BY\n    r.r_name\n  ORDER BY\n    SUM(l.l_extendedprice * (1 - l.l_discount)) DESC\n  LIMIT 2\n)\nAND sales_rank <= 3;"}, {"question": "What are the names of the top three customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_quantity * l.l_extendedprice) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 3"}, {"question": "What is the most common shipment modes for failed orders ?", "answer": "SELECT l_shipmode,\r\n                   count(DISTINCT o.o_orderkey) as failed_orders\r\nFROM   snowflake_sample_data.tpch_sf1.orders o\r\njoin   snowflake_sample_data.tpch_sf1.lineitem l \r\n       ON l.l_orderkey = o.o_orderkey\r\nWHERE  o.o_orderstatus = 'F'\r\nGROUP BY l_shipmode\r\nORDER BY failed_orders desc\r\n"}, {"question": "What are the top 10 customers that have the highest value of returned brass parts in Europe?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * l.l_discount) AS returned_parts_gross_value\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON l.l_suppkey = s.s_suppkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON s.s_nationkey = n.n_nationkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nWHERE l.l_returnflag = 'R' AND r.r_name = 'EUROPE' AND l.l_partkey IN (\n\tSELECT p.p_partkey FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p\n\tWHERE p.p_type LIKE '%BRASS%'\n) \nGROUP BY customer_name\nORDER BY returned_parts_gross_value DESC\nLIMIT 10"}, {"question": "how many orders were placed per week, how many orders were placed 12 weeks ago?", "answer": "SELECT date_trunc('week', o_orderdate) as week_start,\n       count(*) as number_of_orders,\n       count(case when o_orderdate >= dateadd('week', -12, date_trunc('week', current_date())) and\n                       o_orderdate < dateadd('week',\n                                             -11,\n                                             date_trunc('week', current_date())) then o_orderkey\n                  else null end) as number_of_orders_12_weeks_ago\nFROM   snowflake_sample_data.tpch_sf1.orders\nGROUP BY week_start\nORDER BY week_start asc"}, {"question": "What are the total sales for each year for the most popular part?", "answer": "SELECT \n  TO_CHAR(o.o_orderdate, 'YYYY') AS order_year, \n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM \n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p ON l.l_partkey = p.p_partkey\nWHERE \n  p.p_name = (\n    SELECT \n      p_name \n    FROM \n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART \n    JOIN \n      SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM ON p_partkey = l_partkey \n    GROUP BY \n      p_name \n    ORDER BY \n      SUM(l_quantity) DESC \n    LIMIT 1\n  )\nGROUP BY \n  order_year\nORDER BY \n  order_year;"}, {"question": "What's the most recent date in the dataset?", "answer": "SELECT max(o_orderdate) as latest_date\nFROM   snowflake_sample_data.tpch_sf1.orders;"}, {"question": "What are the top ten users?", "answer": "SELECT user_name\r\nFROM   users\r\nORDER BY user_id limit 10;"}, {"question": "Which 5 customers are getting the biggest discounts?", "answer": "SELECT c.c_name AS customer_name, AVG(l.l_discount) AS avg_discount\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY avg_discount DESC\nLIMIT 5"}, {"question": "Calculate the total sales amount and the average sales amount for each nation?", "answer": "SELECT n.n_name as nation_name,\r\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\r\n       avg(l.l_extendedprice * (1 - l.l_discount)) as avg_sales\r\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\r\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\r\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\r\n        ON o.o_orderkey = l.l_orderkey\r\nGROUP BY nation_name\r\nORDER BY 2 asc\r\nlimit 10;"}, {"question": "What are the top 5 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 5"}, {"question": "how many customers in each region?", "answer": "SELECT r.r_name as region_name,\n       count(distinct c.c_custkey) as customer_count\nFROM   snowflake_sample_data.tpch_sf1.region r join snowflake_sample_data.tpch_sf1.nation n\n        ON r.r_regionkey = n.n_regionkey join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nGROUP BY region_name;"}, {"question": "Who are the top 10 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 10"}, {"question": "For each shipping mode ('MAIL' or 'SHIP'), how many orders have a high priority ('1-URGENT' or '2-HIGH') and how many have a low priority (anything other than '1-URGENT' or '2-HIGH')? Consider only orders shipped within 1994 and where the commit date is before the receipt date and the ship date is before the commit date.", "answer": "select\r\n\tl_shipmode,\r\n\tsum(case\r\n\t\twhen o_orderpriority = '1-URGENT'\r\n\t\t\tor o_orderpriority = '2-HIGH'\r\n\t\t\tthen 1\r\n\t\telse 0\r\n\tend) as high_line_count,\r\n\tsum(case\r\n\t\twhen o_orderpriority <> '1-URGENT'\r\n\t\t\tand o_orderpriority <> '2-HIGH'\r\n\t\t\tthen 1\r\n\t\telse 0\r\n\tend) as low_line_count\r\nfrom\r\n\torders,\r\n\tlineitem\r\nwhere\r\n\to_orderkey = l_orderkey\r\n\tand l_shipmode in ('MAIL', 'SHIP')\r\n\tand l_commitdate < l_receiptdate\r\n\tand l_shipdate < l_commitdate\r\n\tand l_receiptdate >= date '1994-01-01'\r\n\tAND o_orderdate < DATEADD(year, 1, '1994-01-01')\r\ngroup by\r\n\tl_shipmode\r\norder by\r\n\tl_shipmode;\r\n"}, {"question": "What are the top 10 customers that have the highest value of returned brass parts in Germany? Use the abbreviation \"DE\" in the chart title instead of Germany", "answer": "This question has already been asked and answered here: https://github.com/AskdataHQ/snowflake-samples/issues/29#issuecomment-891313460\n\nHere's the solution:\n\n```\nSELECT c.c_name AS customer_name, SUM(l.l_extendedprice * l.l_discount) AS returned_parts_gross_value\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON l.l_suppkey = s.s_suppkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON s.s_nationkey = n.n_nationkey\nWHERE l.l_returnflag = 'R' AND n.n_name = 'GERMANY' AND l.l_partkey IN (\n\tSELECT p.p_partkey FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p\n\tWHERE p.p_type LIKE '%BRASS%'\n) \nGROUP BY customer_name\nORDER BY returned_parts_gross_value DESC\nLIMIT 10\n```\n\n"}, {"question": "Which country has the most failed orders ?", "answer": "SELECT n.n_name as country_name,\n       count(o.o_orderkey) as failed_orders\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey\nWHERE  o.o_orderstatus = 'F'\nGROUP BY country_name\nORDER BY failed_orders desc limit 1;"}, {"question": "Retrieve customer names with highest orders", "answer": "SELECT c.c_name as customer_name,\n       count(o.o_orderkey) as order_count\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey\nGROUP BY customer_name\nORDER BY order_count desc"}, {"question": "How many customers are in each region?", "answer": "SELECT r.r_name as region_name,\n       count(distinct c.c_custkey) as customer_count\nFROM   snowflake_sample_data.tpch_sf1.region r join snowflake_sample_data.tpch_sf1.nation n\n        ON r.r_regionkey = n.n_regionkey join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nGROUP BY region_name\nORDER BY region_name asc;"}, {"question": "Which part keys in Germany have a total value of supply cost times available quantity greater than 0.0001% of the total value for all part keys in Germany? I want the results sorted by value in descending order.", "answer": "select\r\n\tps_partkey,\r\n\tsum(ps_supplycost * ps_availqty) as value\r\nfrom\r\n\tpartsupp,\r\n\tsupplier,\r\n\tnation\r\nwhere\r\n\tps_suppkey = s_suppkey\r\n\tand s_nationkey = n_nationkey\r\n\tand n_name = 'GERMANY'\r\ngroup by\r\n\tps_partkey having\r\n\t\tsum(ps_supplycost * ps_availqty) > (\r\n\t\t\tselect\r\n\t\t\t\tsum(ps_supplycost * ps_availqty) * 0.0001000000\r\n\t\t\tfrom\r\n\t\t\t\tpartsupp,\r\n\t\t\t\tsupplier,\r\n\t\t\t\tnation\r\n\t\t\twhere\r\n\t\t\t\tps_suppkey = s_suppkey\r\n\t\t\t\tand s_nationkey = n_nationkey\r\n\t\t\t\tand n_name = 'GERMANY'\r\n\t\t)\r\norder by\r\n\tvalue desc;\r\n"}, {"question": "What are the top 7 customers ranked by total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 7"}, {"question": "which customers have the most total revenue? show customer name, country and number of orders", "answer": "SELECT\n  c.c_name AS customer_name,\n  n.n_name AS country_name,\n  COUNT(DISTINCT o.o_orderkey) AS number_of_orders,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_revenue\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY\n  customer_name,\n  country_name\nORDER BY\n  total_revenue DESC\nLIMIT\n  10;"}, {"question": "近一周大盘用户活跃度", "answer": "Based on the error message, it seems that the table `user_activity` does not exist or the user does not have the necessary permissions to access it. Please ensure that the table exists in the correct database and that the user has the necessary permissions to access it."}, {"question": "how many orders were placed per week?", "answer": "SELECT date_trunc('week', o_orderdate) as week_start,\n       count(*) as number_of_orders\nFROM   snowflake_sample_data.tpch_sf1.orders\nGROUP BY week_start\nORDER BY week_start desc"}, {"question": "What product has the most variability in price in the last 24 months?", "answer": "SELECT p.p_brand,\n       p.p_type,\n       stddev(l.l_extendedprice * (1 - l.l_discount)) as price_std_dev\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.part p\n        ON l.l_partkey = p.p_partkey join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey\nWHERE  o.o_orderdate between dateadd(month, -24, current_date())\n   and current_date()\nGROUP BY p.p_brand, p.p_type\nORDER BY price_std_dev desc limit 10"}, {"question": "Calculate the total order amount for the top 10 nations?", "answer": "SELECT n.n_name as nation_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_order_amount\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey\nGROUP BY nation_name\nORDER BY total_order_amount desc limit 10;"}, {"question": "Who are the top 20 customers by revenue, who have returned items in the last quarter of 1993 (October - December), and what are their details?", "answer": "select\r\n\tc_custkey,\r\n\tc_name,\r\n\tsum(l_extendedprice * (1 - l_discount)) as revenue,\r\n\tc_acctbal,\r\n\tn_name,\r\n\tc_address,\r\n\tc_phone,\r\n\tc_comment\r\nfrom\r\n\tcustomer,\r\n\torders,\r\n\tlineitem,\r\n\tnation\r\nwhere\r\n\tc_custkey = o_custkey\r\n\tand l_orderkey = o_orderkey\r\n\tand o_orderdate >= date '1993-10-01'\r\n\tAND o_orderdate < DATEADD(month, 3, '1993-10-01')\r\n\tand l_returnflag = 'R'\r\n\tand c_nationkey = n_nationkey\r\ngroup by\r\n\tc_custkey,\r\n\tc_name,\r\n\tc_acctbal,\r\n\tc_phone,\r\n\tn_name,\r\n\tc_address,\r\n\tc_comment\r\norder by\r\n\trevenue desc limit 20;"}, {"question": "what are the 5 top customers by sales in europe. show the total sales, total number of orders, country, and date of last order.", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales,\n       count(distinct o.o_orderkey) as total_orders,\n       n.n_name as country,\n       max(o.o_orderdate) as last_order_date\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nWHERE  r.r_name = 'EUROPE'\nGROUP BY customer_name, country\nORDER BY total_sales desc limit 5;"}, {"question": "What are the top 10 customers that have the highest value of returned brass parts where each part is more than $10 in Germany? Use the abbreviation \"DE\" in the chart title instead of Germany", "answer": "SELECT \n    c.c_name AS customer_name, \n    SUM(l.l_extendedprice * l.l_discount) AS returned_parts_gross_value\nFROM \n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o \n        ON l.l_orderkey = o.o_orderkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c \n        ON o.o_custkey = c.c_custkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s \n        ON l.l_suppkey = s.s_suppkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n \n        ON s.s_nationkey = n.n_nationkey\n    WHERE \n        l.l_returnflag = 'R' \n        AND n.n_name = 'GERMANY'\n        AND l.l_extendedprice * (1 - l.l_discount) > 10 \n        AND l.l_partkey IN (\n            SELECT \n                p.p_partkey \n            FROM \n                SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p \n            WHERE \n                p.p_type LIKE '%BRASS%'\n        )\nGROUP BY \n    customer_name\nORDER BY \n    returned_parts_gross_value DESC\nLIMIT \n    10"}, {"question": "What is the market share of Brazil, by year, for 'ECONOMY ANODIZED STEEL' type parts sold in the 'AMERICA' region for the years 1995 and 1996?", "answer": "select\r\n\to_year,\r\n\tsum(case\r\n\t\twhen nation = 'BRAZIL' then volume\r\n\t\telse 0\r\n\tend) / sum(volume) as mkt_share\r\nfrom\r\n\t(\r\n\t\tselect\r\n\t\t\textract(year from o_orderdate) as o_year,\r\n\t\t\tl_extendedprice * (1 - l_discount) as volume,\r\n\t\t\tn2.n_name as nation\r\n\t\tfrom\r\n\t\t\tpart,\r\n\t\t\tsupplier,\r\n\t\t\tlineitem,\r\n\t\t\torders,\r\n\t\t\tcustomer,\r\n\t\t\tnation n1,\r\n\t\t\tnation n2,\r\n\t\t\tregion\r\n\t\twhere\r\n\t\t\tp_partkey = l_partkey\r\n\t\t\tand s_suppkey = l_suppkey\r\n\t\t\tand l_orderkey = o_orderkey\r\n\t\t\tand o_custkey = c_custkey\r\n\t\t\tand c_nationkey = n1.n_nationkey\r\n\t\t\tand n1.n_regionkey = r_regionkey\r\n\t\t\tand r_name = 'AMERICA'\r\n\t\t\tand s_nationkey = n2.n_nationkey\r\n\t\t\tand o_orderdate between date '1995-01-01' and date '1996-12-31'\r\n\t\t\tand p_type = 'ECONOMY ANODIZED STEEL'\r\n\t) as all_nations\r\ngroup by\r\n\to_year\r\norder by\r\n\to_year;\r\n\r\n"}, {"question": "What are the top 10 countries ranked by total sales, based on customer and order data?", "answer": "SELECT\n  n.n_name AS country_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY country_name\nORDER BY total_sales DESC\nLIMIT 10;"}, {"question": "What are the top 11 customers by sales in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 11"}, {"question": "For each year, what is the total profit generated by parts with a name containing 'green' for each nation? I want this information sorted by nation and year in descending order.", "answer": "select\r\n\tnation,\r\n\to_year,\r\n\tsum(amount) as sum_profit\r\nfrom\r\n\t(\r\n\t\tselect\r\n\t\t\tn_name as nation,\r\n\t\t\textract(year from o_orderdate) as o_year,\r\n\t\t\tl_extendedprice * (1 - l_discount) - ps_supplycost * l_quantity as amount\r\n\t\tfrom\r\n\t\t\tpart,\r\n\t\t\tsupplier,\r\n\t\t\tlineitem,\r\n\t\t\tpartsupp,\r\n\t\t\torders,\r\n\t\t\tnation\r\n\t\twhere\r\n\t\t\ts_suppkey = l_suppkey\r\n\t\t\tand ps_suppkey = l_suppkey\r\n\t\t\tand ps_partkey = l_partkey\r\n\t\t\tand p_partkey = l_partkey\r\n\t\t\tand o_orderkey = l_orderkey\r\n\t\t\tand s_nationkey = n_nationkey\r\n\t\t\tand p_name like '%green%'\r\n\t) as profit\r\ngroup by\r\n\tnation,\r\n\to_year\r\norder by\r\n\tnation,\r\n\to_year desc;\r\n"}, {"question": "which customer ordered for the biggest order value, along with the name of the customer and the order value ? ", "answer": "SELECT c.c_name as customer_name,\n       o.o_orderkey as order_key,\n       o.o_totalprice as order_value\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey\nORDER BY o.o_totalprice desc limit 1"}, {"question": "How many customers do we have in the United States?", "answer": "SELECT count(c.c_custkey) as number_of_customers\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nWHERE  n.n_name = 'UNITED STATES'"}, {"question": "Retrieve all sales  for the American region", "answer": "SELECT sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nWHERE  r.r_name = 'AMERICA'\nGROUP BY r.r_name;"}, {"question": "how many orders were placed per day?", "answer": "SELECT date_trunc('day', o_orderdate) as order_date,\n       count(*) as number_of_orders\nFROM   snowflake_sample_data.tpch_sf1.orders\nGROUP BY 1\nORDER BY 1"}, {"question": "what is the name of the 10 biggest suppliers", "answer": "SELECT s_name as supplier_name\nFROM   snowflake_sample_data.tpch_sf1.supplier\nORDER BY s_acctbal desc limit 10;"}, {"question": "What is the average number of items per order?", "answer": "SELECT AVG(total_items) AS avg_order_size\nFROM (\n  SELECT o.o_orderkey AS order_id, SUM(l.l_quantity) AS total_items\n  FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\n  GROUP BY o.o_orderkey\n)"}, {"question": "What are the top two regions by total sales of all orders?", "answer": "SELECT\n  r.r_name AS region_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON r.r_regionkey = n.n_regionkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY region_name\nORDER BY total_sales DESC\nLIMIT 2;"}, {"question": "list top 10 customer order by amount of orders", "answer": "SELECT c.c_name as customer_name,\n       count(o.o_orderkey) as number_of_orders\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey\nGROUP BY customer_name\nORDER BY number_of_orders desc limit 10"}, {"question": "What are the top 10 customers by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales desc limit 10;"}, {"question": "What are the top 3 customers in China?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'CHINA'\nGROUP BY customer_name\nORDER BY total_sales desc limit 3;"}, {"question": "What are the top 10 customers in the US by sales?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 10;"}, {"question": "What are the total sales for each region?", "answer": "SELECT r.r_name as region,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey join snowflake_sample_data.tpch_sf1.region r\n        ON n.n_regionkey = r.r_regionkey\nGROUP BY region\nORDER BY total_sales desc;"}, {"question": "What are the total sales by region?", "answer": "```\nSELECT \n  r.r_name AS region, \n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM \n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY \n  region\nORDER BY \n  total_sales DESC;\n```"}, {"question": "What are the top 10 customers by sales in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 10"}, {"question": "how many orders were placed per week, and how does that compare to 12 weeks prior?", "answer": "SELECT trunc(o_orderdate, 'week') as week_start,\n       count(distinct o_orderkey) as num_orders,\n       count(distinct case when o_orderdate >= dateadd(week, -12, trunc(current_date, 'week')) and\n                                o_orderdate < dateadd(week, -11, trunc(current_date, 'week')) then o_orderkey\n                           else null end) as num_orders_12_weeks_prior,\n       case when count(distinct o_orderkey) > count(distinct case when o_orderdate >= dateadd(week, -12, trunc(current_date, 'week')) and\n                 o_orderdate < dateadd(week, -11, trunc(current_date, 'week')) then o_orderkey\n            else null end) then 'Higher'\n            when count(distinct o_orderkey) < count(distinct case when o_orderdate >= dateadd(week, -12, trunc(current_date, 'week')) and\n                 o_orderdate < dateadd(week, -11, trunc(current_date, 'week')) then o_orderkey\n            else null end) then 'Lower'\n            else 'Same' end as order_diff\nFROM   snowflake_sample_data.tpch_sf1.orders\nGROUP BY week_start having week_start >= dateadd(week, -12, trunc(current_date, 'week'))\nORDER BY week_start desc;"}, {"question": "Can you show me a list of different parts (defined by brand, type, and size), excluding certain types and brands, and those supplied by suppliers with customer complaints? And can you order this list by the number of distinct suppliers for each part, and then by brand, type, and size?", "answer": "select\r\n\tp_brand,\r\n\tp_type,\r\n\tp_size,\r\n\tcount(distinct ps_suppkey) as supplier_cnt\r\nfrom\r\n\tsnowflake_sample_data.tpch_sf1.partsupp,\r\n\tsnowflake_sample_data.tpch_sf1.part\r\nwhere\r\n\tp_partkey = ps_partkey\r\n\tand p_brand <> 'Brand#45'\r\n\tand p_type not like 'MEDIUM POLISHED%'\r\n\tand p_size in (49, 14, 23, 45, 19, 3, 36, 9)\r\n\tand ps_suppkey not in (\r\n\t\tselect\r\n\t\t\ts_suppkey\r\n\t\tfrom\r\n\t\t\tsnowflake_sample_data.tpch_sf1.supplier\r\n\t\twhere\r\n\t\t\ts_comment like '%Customer%Complaints%'\r\n\t)\r\ngroup by\r\n\tp_brand,\r\n\tp_type,\r\n\tp_size\r\norder by\r\n\tsupplier_cnt desc,\r\n\tp_brand,\r\n\tp_type,\r\n\tp_size;"}, {"question": "What are the total sales by region and what are  the region names?", "answer": "```\nSELECT \n  r.r_name AS region_name, \n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM \n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY \n  region_name\nORDER BY \n  total_sales DESC;\n```"}, {"question": "What are the top 5 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 5"}, {"question": "What are the top 3 customers by sales in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.customer c join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 3;"}, {"question": "What are the top 7 customers in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 7"}, {"question": "which country has most customers ? ", "answer": "SELECT n.n_name as country_name,\r\n       o_orderstatus,\r\n       count(c.c_custkey) as customer_count\r\nFROM   snowflake_sample_data.tpch_sf1.nation n\r\njoin snowflake_sample_data.tpch_sf1.customer c\r\n        ON n.n_nationkey = c.c_nationkey\r\nJOIN snowflake_sample_data.tpch_sf1.orders o \r\n        on o.o_custkey = c.c_custkey \r\nGROUP BY country_name, o_orderstatus \r\n"}, {"question": "how many customers are in the united states?", "answer": "SELECT count(c.c_custkey) as number_of_customers\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey\nWHERE  n.n_name = 'UNITED STATES'"}, {"question": "What products have the most variability in price in the last 24 months?", "answer": "SELECT p.p_brand,\n       p.p_type,\n       stddev(l.l_extendedprice * (1 - l.l_discount)) as price_std_dev\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.part p\n        ON l.l_partkey = p.p_partkey join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey\nWHERE  o.o_orderdate between dateadd(month, -24, current_date())\n   and current_date()\nGROUP BY p.p_brand, p.p_type\nORDER BY price_std_dev desc limit 10"}, {"question": "Calculate the total order amount for each nation?", "answer": "SELECT n.n_name as country_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_order_amount\nFROM   snowflake_sample_data.tpch_sf1.nation n join snowflake_sample_data.tpch_sf1.customer c\n        ON n.n_nationkey = c.c_nationkey join snowflake_sample_data.tpch_sf1.orders o\n        ON c.c_custkey = o.o_custkey join snowflake_sample_data.tpch_sf1.lineitem l\n        ON o.o_orderkey = l.l_orderkey\nGROUP BY country_name;"}, {"question": "Which countries had the highest discounts over the last 12 months?", "answer": "SELECT\n  n.n_name AS country_name,\n  AVG(l.l_discount) AS avg_discount\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nWHERE \n  o.o_orderdate BETWEEN DATEADD(month, -12, CURRENT_DATE()) AND CURRENT_DATE()\nGROUP BY\n  country_name\nORDER BY\n  avg_discount DESC;"}, {"question": "What are the top 10 customers that have the highest value of returned brass parts in Africa?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * l.l_discount) AS returned_parts_gross_value\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.SUPPLIER s ON l.l_suppkey = s.s_suppkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON s.s_nationkey = n.n_nationkey\nWHERE l.l_returnflag = 'R' AND n.n_name = 'AFRICA' AND l.l_partkey IN (\n\tSELECT p.p_partkey FROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.PART p\n\tWHERE p.p_type LIKE '%BRASS%'\n) \nGROUP BY customer_name\nORDER BY returned_parts_gross_value DESC\nLIMIT 10"}, {"question": "What are the top 10 customers in terms of total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 10"}, {"question": "top 5 customers by sales in the united states", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 5;"}, {"question": "Who are the top 10 customers in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 10;"}, {"question": "What are the top 20 customers that have the highest value of returned parts?", "answer": "SELECT \n    c.c_name, \n    SUM(l.l_extendedprice * l.l_discount) as returned_parts_gross_value\nFROM \n    SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l \n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n    JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nWHERE \n    l.l_returnflag = 'R'\nGROUP BY \n    c.c_name\nORDER BY \n    returned_parts_gross_value DESC\nLIMIT \n    20"}, {"question": "I have a large table consisting of two keys (id,date) I want to change the id so that every day an account starts from one", "answer": "SELECT dense_rank() OVER (PARTITION BY date\r\n                          ORDER BY id) as new_id,\r\n       date\r\nFROM   your_table;"}, {"question": "What are the top 10 customers?", "answer": "SELECT c.c_name as customer_name\nFROM   snowflake_sample_data.tpch_sf1.customer c\nORDER BY c.c_custkey limit 10;"}, {"question": "What are the top 5 customers in the United States?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'UNITED STATES'\nGROUP BY customer_name\nORDER BY total_sales desc limit 5"}, {"question": "What are the top 10 customers that have the highest value of returned brass parts where each part is more than $10 in Germany?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * l.l_discount) as returned_parts_gross_value\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.supplier s\n        ON l.l_suppkey = s.s_suppkey join snowflake_sample_data.tpch_sf1.nation n\n        ON s.s_nationkey = n.n_nationkey\nWHERE  l.l_returnflag = 'R'\n   and n.n_name = 'GERMANY'\n   and l.l_extendedprice * (1 - l.l_discount) > 10\n   and l.l_partkey in (SELECT p.p_partkey\n                    FROM   snowflake_sample_data.tpch_sf1.part p\n                    WHERE  p.p_type like '%BRASS%')\nGROUP BY customer_name\nORDER BY returned_parts_gross_value desc limit 10"}, {"question": "Can you provide the names and addresses of suppliers located in Canada who supply parts with names beginning with 'forest' and who have more than half of their total quantity supplied for these parts shipped within the year 1994?", "answer": "select\r\n\ts_name,\r\n\ts_address\r\nfrom\r\n\tsupplier,\r\n\tnation\r\nwhere\r\n\ts_suppkey in (\r\n\t\tselect\r\n\t\t\tps_suppkey\r\n\t\tfrom\r\n\t\t\tpartsupp\r\n\t\twhere\r\n\t\t\tps_partkey in (\r\n\t\t\t\tselect\r\n\t\t\t\t\tp_partkey\r\n\t\t\t\tfrom\r\n\t\t\t\t\tpart\r\n\t\t\t\twhere\r\n\t\t\t\t\tp_name like 'forest%'\r\n\t\t\t)\r\n\t\t\tand ps_availqty > (\r\n\t\t\t\tselect\r\n\t\t\t\t\t0.5 * sum(l_quantity)\r\n\t\t\t\tfrom\r\n\t\t\t\t\tlineitem\r\n\t\t\t\twhere\r\n\t\t\t\t\tl_partkey = ps_partkey\r\n\t\t\t\t\tand l_suppkey = ps_suppkey\r\n\t\t\t\t\tand l_shipdate >= date '1994-01-01'\r\n                \tAND l_shipdate < DATEADD(year, 1, '1994-01-01')\r\n\t\t\t)\r\n\t)\r\n\tand s_nationkey = n_nationkey\r\n\tand n_name = 'CANADA'\r\norder by\r\n\ts_name;\r\n"}, {"question": "what are my tables? ", "answer": "You can view your tables by running the following SQL statement:\n\n```\nSELECT table_name FROM information_schema.tables WHERE table_schema = '<your_database_name>';\n```\n\nReplace `<your_database_name>` with the name of your database."}, {"question": "What is the top 3 customers in China?", "answer": "SELECT c.c_name as customer_name,\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\n        ON c.c_nationkey = n.n_nationkey\nWHERE  n.n_name = 'CHINA'\nGROUP BY customer_name\nORDER BY total_sales desc limit 3;"}, {"question": "top 5 countries by sales", "answer": "SELECT n.n_name as country_name,\r\n       sum(l.l_extendedprice * (1 - l.l_discount)) as total_sales\r\nFROM   snowflake_sample_data.tpch_sf1.lineitem l join snowflake_sample_data.tpch_sf1.orders o\r\n        ON l.l_orderkey = o.o_orderkey join snowflake_sample_data.tpch_sf1.customer c\r\n        ON o.o_custkey = c.c_custkey join snowflake_sample_data.tpch_sf1.nation n\r\n        ON c.c_nationkey = n.n_nationkey\r\nGROUP BY country_name\r\nORDER BY total_sales desc limit 5;"}, {"question": "What are the names of the top 3 customers with the highest total sales?", "answer": "SELECT c.c_name AS customer_name, SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\nJOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\nGROUP BY customer_name\nORDER BY total_sales DESC\nLIMIT 3"}, {"question": "What are the total sales by country for the top 10 countries in descending order?", "answer": "SELECT\n  n.n_name AS country_name,\n  SUM(l.l_extendedprice * (1 - l.l_discount)) AS total_sales\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON n.n_nationkey = c.c_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON c.c_custkey = o.o_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l ON o.o_orderkey = l.l_orderkey\nGROUP BY\n  country_name\nORDER BY\n  total_sales DESC\nLIMIT 10;"}, {"question": "What is the average discount for each region?", "answer": "SELECT\n  r.r_name AS region_name,\n  AVG(l.l_discount) AS avg_discount\nFROM\n  SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.LINEITEM l\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.ORDERS o ON l.l_orderkey = o.o_orderkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.CUSTOMER c ON o.o_custkey = c.c_custkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.NATION n ON c.c_nationkey = n.n_nationkey\n  JOIN SNOWFLAKE_SAMPLE_DATA.TPCH_SF1.REGION r ON n.n_regionkey = r.r_regionkey\nGROUP BY\n  region_name;"}]